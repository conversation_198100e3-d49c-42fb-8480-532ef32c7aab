services:
  app1:
    build: .
    env_file: .env
    deploy:
      resources:
        limits:
          cpus: "1.8"  # Updated to 2 vCPU cores (allowing some overhead)
          memory: 3g # Updated to use most of 4GB RAM (leaving some for system)
    ports:
      - "3000:3000"
    restart: always
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/healthz"]
      interval: 45s  # Increased for India server stability
      timeout: 20s   # Increased timeout for India
      retries: 3     # Reduced retries to prevent restart loops
      start_period: 60s  # Longer start period for India
    # Add extra_hosts to allow container to access host services
    extra_hosts:
      - "host.docker.internal:host-gateway"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - INSTANCE_ID=india-1
      - REGION=india
      # Connect to PostgreSQL on VPS host (from Docker container)
      - PGHOST=host.docker.internal
      - PGPORT=5432
      - PGDATABASE=tempfly_app
      - PGUSER=postgres
      - PGPASSWORD=4wyWCAAk92hkGUhdh7
      - PGSSLMODE=${PGSSLMODE:-disable}
      - DATABASE_URL=${DATABASE_URL}
      # India-specific database connection settings (optimized for 2 vCPU)
      - DB_CONNECTION_TIMEOUT=25000  # 25s for India (vs 10s default)
      - DB_ACQUIRE_TIMEOUT=12500     # 12.5s for India (vs 5s default)
      - DB_STATEMENT_TIMEOUT=112500  # 112.5s for India (vs 45s default)
      - DB_QUERY_TIMEOUT=112500      # 112.5s for India (vs 45s default)
      - DB_POOL_MAX=12               # Increased for 2 vCPU server (was 8)
      - DB_POOL_MIN=2                # Increased minimum connections
      - DB_IDLE_TIMEOUT=120000       # 120s idle timeout for India (increased)
      # Health check settings for India (optimized)
      - DB_HEALTH_CHECK_INTERVAL=60000  # 60s health check interval (increased)
      - DB_MAX_CONSECUTIVE_FAILURES=8   # More tolerance for failures
      - DB_MAX_RETRIES=5                # More retries for India
      # Redis configuration (local Docker service)
      - REDIS_ENABLED=${REDIS_ENABLED:-true}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - REDIS_DEFAULT_TTL=${REDIS_DEFAULT_TTL:-7200}
      - REDIS_CONNECT_TIMEOUT=10000     # Increased Redis timeout for India
      - REDIS_COMMAND_TIMEOUT=8000      # Increased Redis command timeout
      # Application settings optimized for 2 vCPU India server
      - CONNECTION_WARMUP_INTERVAL=120000 # 120s warmup interval for India (less aggressive)
      - MEMORY_CACHE_SIZE=${MEMORY_CACHE_SIZE:-300}  # Increased cache for 2 vCPU
      - MAX_MEMORY_CACHE_ITEMS=${MAX_MEMORY_CACHE_ITEMS:-1500}  # Increased for 2 vCPU
      # Logging settings for India (more verbose for debugging)
      - LOG_LEVEL=info
      - ENABLE_REQUEST_LOGGING=true
      - ENABLE_PERFORMANCE_MONITORING=true  # Enable for India debugging
      # Rate limiting adjusted for 2 vCPU India server
      - RATE_LIMIT_MAX_REQUESTS=400         # Increased for 2 vCPU server
      - RATE_LIMIT_WINDOW_MS=60000
      # Other environment variables
      - API_URL=${API_URL}
      - INTERNAL_API_KEY=${INTERNAL_API_KEY}
      - ENCRYPTION_KEY=${ENCRYPTION_KEY}
      - RAPIDAPI_PROXY_SECRET=${RAPIDAPI_PROXY_SECRET}
      - ADMIN_API_KEY=${ADMIN_API_KEY}
      - DB_AUTO_MIGRATE=${DB_AUTO_MIGRATE:-true}
      - DB_INIT_ON_START=${DB_INIT_ON_START:-true}
      - ENABLE_ATTACHMENTS=${ENABLE_ATTACHMENTS:-true}
      - ENABLE_DOMAIN_PRELOADING=${ENABLE_DOMAIN_PRELOADING:-true}
      - FORCE_CACHE=${FORCE_CACHE:-true}
      - FREE_USER_INBOX_LIMIT=${FREE_USER_INBOX_LIMIT:-10}
      - MAX_LOG_FILES=${MAX_LOG_FILES:-15}  # More log files for India debugging
      - MAX_LOG_SIZE=${MAX_LOG_SIZE:-10}    # Larger log files for India
    volumes:
      - type: bind
        source: ./logs
        target: /app/logs
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - tempmail-network

  redis:
    image: redis:7-alpine
    deploy:
      resources:
        limits:
          cpus: "0.4"  # Increased CPU for Redis on 2 vCPU server
          memory: 768m # Increased memory for Redis
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    command: ["redis-server", "/usr/local/etc/redis/redis.conf", "--requirepass", "${REDIS_PASSWORD}"]
    restart: always
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "ping"]
      interval: 60s  # Increased for India stability
      timeout: 15s   # Increased timeout
      retries: 3     # Reduced retries to prevent restart loops
      start_period: 20s
    networks:
      - tempmail-network

volumes:
  redis_data:

networks:
  tempmail-network:
    driver: bridge
