services:
  app1:
    build: .
    env_file: .env
    deploy:
      resources:
        limits:
          cpus: "0.9"    # Use most of the single CPU
          memory: 1.5g   # Conservative memory allocation
    ports:
      - "3000:3000"
    restart: always
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/healthz"]
      interval: 45s      # Longer intervals to reduce CPU usage
      timeout: 20s       # Extended timeout for single-CPU
      retries: 3         # Fewer retries to reduce load
      start_period: 60s  # Longer start period for single-CPU
    # Add extra_hosts to allow container to access host services
    extra_hosts:
      - "host.docker.internal:host-gateway"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - INSTANCE_ID=india-minimal
      - REGION=india
      # Connect to PostgreSQL on VPS host (from Docker container)
      - PGHOST=host.docker.internal
      - PGPORT=5432
      - PGDATABASE=tempfly_app
      - PGUSER=postgres
      - PGPASSWORD=4wyWCAAk92hkGUhdh7
      - PGSSLMODE=${PGSSLMODE:-disable}
      - DATABASE_URL=${DATABASE_URL}
      # Minimal database connection settings for single-CPU
      - DB_CONNECTION_TIMEOUT=30000  # 30s timeout for single-CPU
      - DB_ACQUIRE_TIMEOUT=15000     # 15s acquire timeout
      - DB_STATEMENT_TIMEOUT=120000  # 2 minutes for statements
      - DB_QUERY_TIMEOUT=120000      # 2 minutes for queries
      - DB_POOL_MAX=5                # Very small pool for single-CPU
      - DB_POOL_MIN=1                # Single minimum connection
      - DB_IDLE_TIMEOUT=120000       # 2 minutes idle timeout
      # Health check settings for minimal server
      - DB_HEALTH_CHECK_INTERVAL=60000   # 60s health check interval
      - DB_MAX_CONSECUTIVE_FAILURES=7    # More tolerance for single-CPU
      - DB_MAX_RETRIES=3                 # Fewer retries to reduce load
      # Redis configuration (local Docker service)
      - REDIS_ENABLED=${REDIS_ENABLED:-true}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - REDIS_DEFAULT_TTL=${REDIS_DEFAULT_TTL:-7200}
      - REDIS_CONNECT_TIMEOUT=15000      # Extended Redis timeout
      - REDIS_COMMAND_TIMEOUT=10000      # Extended Redis command timeout
      # Application settings optimized for minimal server
      - CONNECTION_WARMUP_INTERVAL=90000  # 90s warmup interval
      - MEMORY_CACHE_SIZE=100             # Minimal cache size
      - MAX_MEMORY_CACHE_ITEMS=500        # Minimal cache items
      # Logging settings (reduced for performance)
      - LOG_LEVEL=warn                    # Less verbose logging
      - ENABLE_REQUEST_LOGGING=false      # Disable request logging for performance
      - ENABLE_PERFORMANCE_MONITORING=false # Disable performance monitoring
      # Rate limiting for minimal server
      - RATE_LIMIT_MAX_REQUESTS=100       # Very conservative rate limiting
      - RATE_LIMIT_WINDOW_MS=60000
      # Other environment variables
      - API_URL=${API_URL}
      - INTERNAL_API_KEY=${INTERNAL_API_KEY}
      - ENCRYPTION_KEY=${ENCRYPTION_KEY}
      - RAPIDAPI_PROXY_SECRET=${RAPIDAPI_PROXY_SECRET}
      - ADMIN_API_KEY=${ADMIN_API_KEY}
      - DB_AUTO_MIGRATE=${DB_AUTO_MIGRATE:-false}  # Disable auto-migrate for performance
      - DB_INIT_ON_START=${DB_INIT_ON_START:-false} # Disable init for performance
      - ENABLE_ATTACHMENTS=${ENABLE_ATTACHMENTS:-false} # Disable attachments for performance
      - ENABLE_DOMAIN_PRELOADING=${ENABLE_DOMAIN_PRELOADING:-false} # Disable preloading
      - FORCE_CACHE=${FORCE_CACHE:-true}
      - FREE_USER_INBOX_LIMIT=${FREE_USER_INBOX_LIMIT:-5} # Reduced limit
      - MAX_LOG_FILES=${MAX_LOG_FILES:-5}    # Fewer log files
      - MAX_LOG_SIZE=${MAX_LOG_SIZE:-5}      # Smaller log files
    volumes:
      - type: bind
        source: ./logs
        target: /app/logs
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - tempmail-network

  redis:
    image: redis:7-alpine
    deploy:
      resources:
        limits:
          cpus: "0.1"    # Minimal CPU for Redis
          memory: 256m   # Minimal memory for Redis
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    command: ["redis-server", "/usr/local/etc/redis/redis.conf", "--requirepass", "${REDIS_PASSWORD}", "--maxmemory", "200mb", "--maxmemory-policy", "allkeys-lru"]
    restart: always
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "ping"]
      interval: 60s      # Longer intervals for minimal server
      timeout: 15s       # Extended timeout
      retries: 3         # Fewer retries
      start_period: 20s
    networks:
      - tempmail-network

volumes:
  redis_data:

networks:
  tempmail-network:
    driver: bridge
