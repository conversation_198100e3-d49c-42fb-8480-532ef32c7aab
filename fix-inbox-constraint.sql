-- Fix for TempFly.io Inbox Data Consistency Issue
-- This script modifies the unique constraint to allow recreation of expired inboxes

-- Step 1: Drop the existing unique constraint on address
ALTER TABLE inboxes DROP CONSTRAINT IF EXISTS inboxes_address_key;

-- Step 2: Create a partial unique index that only applies to active inboxes
-- This allows expired/inactive inboxes to have duplicate addresses
CREATE UNIQUE INDEX CONCURRENTLY idx_inboxes_address_active_unique 
ON inboxes (address) 
WHERE is_active = true;

-- Step 3: Create maintenance_logs table for tracking cleanup operations
CREATE TABLE IF NOT EXISTS maintenance_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    operation VARCHAR(255) NOT NULL,
    details TEXT,
    affected_rows INTEGER DEFAULT 0,
    execution_time INTEGER DEFAULT 0,
    status VARCHAR(50) DEFAULT 'success',
    error_message TEXT,
    executed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Step 4: Create index for maintenance_logs
CREATE INDEX IF NOT EXISTS idx_maintenance_logs_operation ON maintenance_logs(operation);
CREATE INDEX IF NOT EXISTS idx_maintenance_logs_executed_at ON maintenance_logs(executed_at);

-- Step 5: Enable pg_cron extension (requires superuser privileges)
-- CREATE EXTENSION IF NOT EXISTS pg_cron;

-- Step 6: Create a function for automated cleanup
CREATE OR REPLACE FUNCTION cleanup_expired_inboxes()
RETURNS INTEGER AS $$
DECLARE
    affected_count INTEGER;
    start_time TIMESTAMP;
    execution_time INTEGER;
BEGIN
    start_time := clock_timestamp();
    
    -- Mark expired inboxes as inactive
    UPDATE inboxes
    SET is_active = false
    WHERE expiry_date IS NOT NULL
    AND expiry_date < NOW()
    AND is_active = true;
    
    GET DIAGNOSTICS affected_count = ROW_COUNT;
    execution_time := EXTRACT(EPOCH FROM (clock_timestamp() - start_time)) * 1000;
    
    -- Log the operation
    INSERT INTO maintenance_logs (
        operation,
        details,
        affected_rows,
        execution_time,
        status
    ) VALUES (
        'cleanup_expired_inboxes_cron',
        'Automated cleanup of expired inboxes',
        affected_count,
        execution_time,
        'success'
    );
    
    RETURN affected_count;
EXCEPTION
    WHEN OTHERS THEN
        -- Log the error
        INSERT INTO maintenance_logs (
            operation,
            details,
            affected_rows,
            execution_time,
            status,
            error_message
        ) VALUES (
            'cleanup_expired_inboxes_cron',
            'Automated cleanup of expired inboxes - FAILED',
            0,
            EXTRACT(EPOCH FROM (clock_timestamp() - start_time)) * 1000,
            'error',
            SQLERRM
        );
        
        RAISE;
END;
$$ LANGUAGE plpgsql;

-- Step 7: Schedule the cleanup job to run every hour
-- Note: This requires pg_cron extension and superuser privileges
-- Uncomment the following line after enabling pg_cron:
-- SELECT cron.schedule('cleanup-expired-inboxes', '0 * * * *', 'SELECT cleanup_expired_inboxes();');

-- Step 8: Create a function for hard deletion of old inactive inboxes (optional)
CREATE OR REPLACE FUNCTION delete_old_inactive_inboxes(days_old INTEGER DEFAULT 30)
RETURNS INTEGER AS $$
DECLARE
    affected_count INTEGER;
    start_time TIMESTAMP;
    execution_time INTEGER;
BEGIN
    start_time := clock_timestamp();
    
    -- Delete inactive inboxes older than specified days
    -- This also cascades to delete associated emails and attachments
    DELETE FROM inboxes
    WHERE is_active = false
    AND updated_at < NOW() - INTERVAL '1 day' * days_old;
    
    GET DIAGNOSTICS affected_count = ROW_COUNT;
    execution_time := EXTRACT(EPOCH FROM (clock_timestamp() - start_time)) * 1000;
    
    -- Log the operation
    INSERT INTO maintenance_logs (
        operation,
        details,
        affected_rows,
        execution_time,
        status
    ) VALUES (
        'delete_old_inactive_inboxes',
        FORMAT('Hard deletion of inactive inboxes older than %s days', days_old),
        affected_count,
        execution_time,
        'success'
    );
    
    RETURN affected_count;
EXCEPTION
    WHEN OTHERS THEN
        -- Log the error
        INSERT INTO maintenance_logs (
            operation,
            details,
            affected_rows,
            execution_time,
            status,
            error_message
        ) VALUES (
            'delete_old_inactive_inboxes',
            FORMAT('Hard deletion of inactive inboxes older than %s days - FAILED', days_old),
            0,
            EXTRACT(EPOCH FROM (clock_timestamp() - start_time)) * 1000,
            'error',
            SQLERRM
        );
        
        RAISE;
END;
$$ LANGUAGE plpgsql;

-- Step 9: Verify the changes
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 'inboxes' 
AND indexname LIKE '%address%';

-- Step 10: Test the fix by checking for duplicate addresses with different active states
SELECT 
    address,
    is_active,
    expiry_date,
    COUNT(*) as count
FROM inboxes 
GROUP BY address, is_active, expiry_date
HAVING COUNT(*) > 1;

COMMIT;
