import { Request, Response, NextFunction } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { AppError } from '../middlewares/errorHandler';
import { InboxModel } from '../models/Inbox';
import { EmailModel, Email, EmailAttachment } from '../models/Email';
import logger from '../utils/logger';
import { detectSpam } from '../utils/spamDetector';
// import { forwardEmail } from '../utils/emailSender'; // Forwarding functionality disabled for now
import pool from '../config/database';
import { getCache, setCache, deleteCache, isRedisConnected } from '../config/redis-direct';
import { validateAttachment, sanitizeFilename } from '../utils/attachmentValidator';
import { cleanTextContent, getCleaningStats } from '../utils/textCleaner';

/**
 * Process incoming email from SMTP server
 */
export const processEmail = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    // Extract email data from request body
    const {
      to_address,
      from_address,
      from_name,
      subject,
      text_content,
      html_content,
      headers,
      attachments
    } = req.body;

    // Validate required fields
    if (!to_address || !from_address) {
      return next(new AppError('Missing required fields: to_address, from_address', 400));
    }

    // Extract inbox name and domain from to_address
    const toMatch = to_address.match(/^([^@]+)@([^@]+)$/);
    if (!toMatch) {
      return next(new AppError('Invalid to_address format', 400));
    }

    const [, inboxName, inboxDomain] = toMatch;

    // Find the inbox
    const inbox = await InboxModel.getByEmail(`${inboxName}@${inboxDomain}`);
    if (!inbox) {
      return next(new AppError(`Inbox not found for ${to_address}`, 404));
    }

    // Clean text content for internal processing with safety measures
    let cleanedTextContent: string | null = null;
    try {
      // Add timeout protection and size limits
      if (!text_content || text_content.length > 50000) {
        // Skip cleaning for very large texts to prevent timeouts
        cleanedTextContent = text_content;
      } else {
        cleanedTextContent = cleanTextContent(text_content);
      }

      // Log cleaning statistics if significant cleaning occurred (with error protection)
      if (text_content && cleanedTextContent !== text_content && text_content.length < 10000) {
        try {
          const stats = getCleaningStats(text_content, cleanedTextContent);
          if (stats.reductionPercentage > 10) { // Only log if significant reduction
            logger.info(`Text cleaning: ${stats.reductionPercentage}% reduction (${stats.originalLength}->${stats.cleanedLength}) inbox:${inbox.id}`);
          }
        } catch (statsError: any) {
          logger.warn('Text cleaning stats error:', statsError?.message || 'Unknown stats error');
        }
      }
    } catch (cleaningError: any) {
      logger.error('Text cleaning failed, using original text:', cleaningError?.message || 'Unknown cleaning error');
      cleanedTextContent = text_content; // Fallback to original text
    }

    // Calculate spam score using cleaned content
    const spamScore = detectSpam(
      from_address,
      subject || '',
      cleanedTextContent || '',
      html_content || ''
    );

    // Prepare email data with cleaned text content
    const emailData: Email = {
      inbox_id: inbox.id,
      from_address,
      from_name: from_name || null,
      to_address,
      subject: subject || null,
      text_content: cleanedTextContent || undefined,
      html_content: html_content || null,
      headers: headers || null,
      has_attachments: attachments && attachments.length > 0,
      is_deleted: false,
      spam_score: spamScore
    };

    // Prepare attachments if any
    if (attachments && attachments.length > 0) {
      // Filter attachments based on security rules
      const safeAttachments: EmailAttachment[] = [];

      for (const attachment of attachments) {
        // Sanitize filename
        const sanitizedFilename = sanitizeFilename(attachment.filename || 'unnamed-attachment');

        // Validate attachment
        const validation = validateAttachment({
          filename: sanitizedFilename,
          content_type: attachment.content_type || 'application/octet-stream'
        });

        if (validation.isValid) {
          // Add to safe attachments
          safeAttachments.push({
            email_id: '', // Will be set after email is created
            filename: sanitizedFilename,
            content_type: attachment.content_type || 'application/octet-stream',
            size: attachment.size || 0,
            content_id: attachment.content_id || null,
            is_inline: attachment.is_inline || false
          });
        } else {
          // Log blocked attachment
          logger.warn(
            `[API] Blocked attachment '${sanitizedFilename}' (${attachment.content_type}): ${validation.reason}`
          );
        }
      }

      // Update email with filtered attachments
      emailData.attachments = safeAttachments;
      emailData.has_attachments = safeAttachments.length > 0;

      // Log attachment filtering results
      if (attachments.length !== safeAttachments.length) {
        logger.info(
          `[API] Filtered attachments: ${safeAttachments.length} of ${attachments.length} allowed`
        );
      }
    }

    // Start processing email - respond to SMTP server immediately
    // This allows the SMTP server to continue processing other emails
    // while we handle the database operations and forwarding
    const emailId = await EmailModel.generateId();

    // Return success response immediately
    res.status(201).json({
      status: 'success',
      message: 'Email accepted for processing',
      data: {
        email_id: emailId,
        inbox_id: inbox.id,
        spam_score: spamScore
      }
    });

    // Continue processing in the background
    // This prevents the SMTP server from waiting for all operations to complete
    Promise.all([
      // 1. Save email to database
      EmailModel.create({...emailData, id: emailId}),

      // 2. Invalidate cache if Redis is connected
      isRedisConnected() ? (async () => {
        try {
          // Invalidate inbox info cache
          const inboxCacheKey = getInboxInfoCacheKey(to_address);
          await deleteCache(inboxCacheKey);

          // Invalidate email list cache for this inbox
          await deleteCache(`emails:inbox:${inbox.id}:*`);

          logger.debug(`Cache invalidated for inbox ${inbox.id} after new email`);
        } catch (cacheError: any) {
          // Log but don't fail if cache invalidation fails
          logger.error('Failed to invalidate cache:', cacheError);
        }
      })() : Promise.resolve(),

      // 3. Forward email if enabled - Forwarding functionality disabled for now
      /*
      (inbox.forwarding_enabled && inbox.forwarding_verified && inbox.forwarding_email) ?
        forwardEmail(
          inbox.forwarding_email,
          from_address,
          subject || 'No Subject',
          text_content || '',
          html_content || text_content || '',
          headers || {},
          emailData.attachments
        ) : Promise.resolve()
      */
      Promise.resolve()
    ])
    .then(() => {
      logger.info(`Email ${emailId} processed successfully in background`);
    })
    .catch((error: any) => {
      // Log the basic error first
      logger.error(`Error in background processing for email ${emailId}:`, error);

      // Then log additional details as a separate message
      if (error.code || error.detail || error.constraint) {
        const details = {
          code: error.code,
          detail: error.detail || error.details,
          constraint: error.constraint
        };
        logger.error(`Additional error details for ${emailId}: ${JSON.stringify(details)}`);
      }

      // Log full serialized error for debugging
      try {
        const fullError = JSON.stringify(error, Object.getOwnPropertyNames(error));
        logger.error(`Full error serialization for ${emailId}: ${fullError}`);
      } catch (err) {
        const serializationError = err instanceof Error ? err : new Error(String(err));
        logger.error(`Could not serialize full error for ${emailId}`, serializationError);
      }
    });

    // Response already sent above
    // We're now processing in the background
  } catch (error: any) {
    logger.error('Error processing email:', error);

    // If it's already an AppError, pass it along
    if (error instanceof AppError) {
      return next(error);
    }

    // Otherwise, create a generic error
    next(new AppError('Failed to process email', 500));
  }
};

/**
 * Cache TTL for inbox info (5 minutes)
 */
const INBOX_INFO_CACHE_TTL = 300;

/**
 * Generate cache key for inbox info
 * @param email - Email address
 * @returns Cache key
 */
const getInboxInfoCacheKey = (email: string): string => {
  return `inbox:info:${email}`;
};

/**
 * Get inbox information for SMTP server
 */
export const getInboxInfo = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { email } = req.params;

    // Validate email format
    const emailMatch = email.match(/^([^@]+)@([^@]+)$/);
    if (!emailMatch) {
      return next(new AppError('Invalid email format', 400));
    }

    const [, name, domain] = emailMatch;
    const fullEmail = `${name}@${domain}`;

    // Try to get inbox info from cache if Redis is connected
    let inbox;
    let fromCache = false;
    const cacheKey = getInboxInfoCacheKey(fullEmail);

    if (isRedisConnected()) {
      const cachedInbox = await getCache<any>(cacheKey);
      if (cachedInbox) {
        inbox = cachedInbox;
        fromCache = true;
      }
    }

    // If not in cache, get from database
    if (!inbox) {
      inbox = await InboxModel.getByEmail(fullEmail);

      // Cache the result if Redis is connected and inbox exists
      if (isRedisConnected() && inbox) {
        await setCache(cacheKey, inbox, INBOX_INFO_CACHE_TTL);
      }
    }

    if (!inbox) {
      // Get request ID from middleware or generate new one
      const requestId = (req as any).requestId || uuidv4().toUpperCase();

      const response = {
        'request-id': requestId,
        'message': 'Success',
        data: {
          exists: false
        }
      };

      return res.status(200).json(response);
    }

    // Check if inbox is expired (do this check even if from cache)
    const isExpired = inbox.expires_at ? new Date(inbox.expires_at) < new Date() : false;

    // Get request ID from middleware or generate new one
    const requestId = (req as any).requestId || uuidv4().toUpperCase();

    // Prepare response
    const response = {
      'request-id': requestId,
      'message': 'Success',
      data: {
        exists: true,
        inbox_id: inbox.id,
        active: inbox.is_active,
        expired: isExpired
      }
    };

    // Return inbox info
    res.status(200).json(response);
  } catch (error: any) {
    logger.error('Error getting inbox info:', error);
    next(new AppError('Failed to get inbox information', 500));
  }
};


