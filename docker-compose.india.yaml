services:
  app1:
    build: .
    env_file: .env
    deploy:
      resources:
        limits:
          cpus: "2"
          memory: 4g  # Increased memory for India region
        reservations:
          cpus: "1"
          memory: 2g
    ports:
      - "3000:3000"
    restart: always
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/healthz"]
      interval: 30s  # More frequent health checks for India
      timeout: 15s   # Longer timeout for high-latency region
      retries: 5     # More retries for India
      start_period: 45s  # Longer start period
    # Add extra_hosts to allow container to access host services
    extra_hosts:
      - "host.docker.internal:host-gateway"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - INSTANCE_ID=india-1
      - REGION=india
      # Database configuration optimized for India region
      - PGHOST=${PGHOST:-*************}  # India database server
      - PGPORT=5432
      - PGDATABASE=tempfly_app
      - PGUSER=postgres
      - PGPASSWORD=${PGPASSWORD}
      - PGSSLMODE=${PGSSLMODE:-disable}
      - DATABASE_URL=${DATABASE_URL}
      # Optimized pool settings for high-latency region
      - DB_POOL_MAX=15              # Reduced max connections
      - DB_POOL_MIN=2               # Lower minimum
      - DB_IDLE_TIMEOUT=90000       # 90 seconds idle timeout
      - DB_CONNECTION_TIMEOUT=25000 # 25 second connection timeout
      - DB_ACQUIRE_TIMEOUT=12500    # 12.5 second acquire timeout
      - DB_STATEMENT_TIMEOUT=112500 # 112.5 second statement timeout
      - DB_QUERY_TIMEOUT=112500     # 112.5 second query timeout
      - DB_MAX_RETRIES=5            # More retries for India
      - DB_HEALTH_CHECK_INTERVAL=15000  # More frequent health checks (15s)
      - DB_MAX_CONSECUTIVE_FAILURES=5   # Allow more failures before marking unhealthy
      - DB_CONNECTION_WARMUP_INTERVAL=180000  # Warm up connections every 3 minutes
      - DB_POOL_RECOVERY_INTERVAL=30000       # Attempt recovery every 30 seconds
      - DB_EMERGENCY_RECREATE_THRESHOLD=8     # Recreate pool after 8 failures
      # Redis configuration (local Docker service)
      - REDIS_ENABLED=${REDIS_ENABLED:-true}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - REDIS_DEFAULT_TTL=${REDIS_DEFAULT_TTL:-7200}
      - REDIS_CONNECT_TIMEOUT=8000  # Longer Redis timeout for India
      - REDIS_COMMAND_TIMEOUT=5000
      - REDIS_RETRY_ATTEMPTS=5
      # Application settings optimized for India
      - CONNECTION_WARMUP_INTERVAL=${CONNECTION_WARMUP_INTERVAL:-30000}  # More frequent warmup
      - MEMORY_CACHE_SIZE=${MEMORY_CACHE_SIZE:-300}  # Larger cache for India
      - MAX_MEMORY_CACHE_ITEMS=${MAX_MEMORY_CACHE_ITEMS:-1500}
      - API_URL=${API_URL}
      - INTERNAL_API_KEY=${INTERNAL_API_KEY}
      - ENCRYPTION_KEY=${ENCRYPTION_KEY}
      - RAPIDAPI_PROXY_SECRET=${RAPIDAPI_PROXY_SECRET}
      # Additional environment variables from .env
      - ADMIN_API_KEY=${ADMIN_API_KEY}
      - DB_AUTO_MIGRATE=${DB_AUTO_MIGRATE:-true}
      - DB_INIT_ON_START=${DB_INIT_ON_START:-true}
      - ENABLE_ATTACHMENTS=${ENABLE_ATTACHMENTS:-true}
      - ENABLE_DOMAIN_PRELOADING=${ENABLE_DOMAIN_PRELOADING:-true}
      - ENABLE_PERFORMANCE_MONITORING=${ENABLE_PERFORMANCE_MONITORING:-true}  # Enable for India
      - ENABLE_REQUEST_LOGGING=${ENABLE_REQUEST_LOGGING:-true}
      - FORCE_CACHE=${FORCE_CACHE:-true}
      - FREE_USER_INBOX_LIMIT=${FREE_USER_INBOX_LIMIT:-10}
      - LOG_LEVEL=${LOG_LEVEL:-info}
      - MAX_LOG_FILES=${MAX_LOG_FILES:-15}  # More log files for debugging
      - MAX_LOG_SIZE=${MAX_LOG_SIZE:-10}    # Larger log files
      - RATE_LIMIT_MAX_REQUESTS=${RATE_LIMIT_MAX_REQUESTS:-400}  # Lower rate limit for India
      - RATE_LIMIT_WINDOW_MS=${RATE_LIMIT_WINDOW_MS:-60000}
      # Monitoring and alerting
      - ENABLE_DATABASE_HEALTH_MONITORING=true
      - DATABASE_HEALTH_LOG_INTERVAL=60000  # Log health every minute
      - ALERT_ON_HIGH_LATENCY=true
      - HIGH_LATENCY_THRESHOLD=10000  # Alert if latency > 10 seconds
      - ALERT_ON_HIGH_UTILIZATION=true
      - HIGH_UTILIZATION_THRESHOLD=80  # Alert if pool utilization > 80%
    volumes:
      - type: bind
        source: ./logs
        target: /app/logs
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - tempmail-network

  redis:
    image: redis:7-alpine
    deploy:
      resources:
        limits:
          cpus: "1"
          memory: 1.5g  # Slightly more memory for India
        reservations:
          cpus: "0.5"
          memory: 512m
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    command: ["redis-server", "/usr/local/etc/redis/redis.conf", "--requirepass", "${REDIS_PASSWORD}"]
    restart: always
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "ping"]
      interval: 30s
      timeout: 10s  # Longer timeout for India
      retries: 5    # More retries
      start_period: 15s
    networks:
      - tempmail-network

volumes:
  redis_data:

networks:
  tempmail-network:
    driver: bridge
