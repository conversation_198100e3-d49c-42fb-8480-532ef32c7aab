import { Pool, PoolClient } from 'pg';
import dotenv from 'dotenv';
import logger from '../utils/logger';

// Load environment variables
dotenv.config();

// Connection health status
interface ConnectionHealth {
  isHealthy: boolean;
  lastCheck: Date;
  consecutiveFailures: number;
}

// Pool configuration interface
interface PoolConfig {
  host: string;
  user: string;
  password: string;
  database: string;
  port: number;
  ssl: any;
  max: number;
  min: number;
  idleTimeoutMillis: number;
  connectionTimeoutMillis: number;
  acquireTimeoutMillis: number;
  keepAlive: boolean;
  statement_timeout: number;
  query_timeout: number;
  application_name: string;
}

// Enhanced failover configuration with regional awareness
const FAILOVER_CONFIG = {
  maxRetries: parseInt(process.env.DB_MAX_RETRIES || '3'),
  retryDelays: [1000, 2000, 4000], // 1s, 2s, 4s exponential backoff
  healthCheckInterval: parseInt(process.env.DB_HEALTH_CHECK_INTERVAL || '30000'), // 30 seconds
  maxConsecutiveFailures: parseInt(process.env.DB_MAX_CONSECUTIVE_FAILURES || '3'),
  connectionWarmupInterval: parseInt(process.env.DB_CONNECTION_WARMUP_INTERVAL || '300000'), // 5 minutes
  poolRecoveryInterval: parseInt(process.env.DB_POOL_RECOVERY_INTERVAL || '60000'), // 1 minute
  emergencyPoolRecreateThreshold: parseInt(process.env.DB_EMERGENCY_RECREATE_THRESHOLD || '10'), // Recreate pool after 10 consecutive failures
};

// Circuit breaker state
interface CircuitBreakerState {
  isOpen: boolean;
  failureCount: number;
  lastFailureTime: Date;
  nextRetryTime: Date;
}

const writePoolCircuitBreaker: CircuitBreakerState = {
  isOpen: false,
  failureCount: 0,
  lastFailureTime: new Date(0),
  nextRetryTime: new Date(0),
};

const readPoolCircuitBreaker: CircuitBreakerState = {
  isOpen: false,
  failureCount: 0,
  lastFailureTime: new Date(0),
  nextRetryTime: new Date(0),
};

// Track connection health
const writePoolHealth: ConnectionHealth = {
  isHealthy: true,
  lastCheck: new Date(),
  consecutiveFailures: 0,
};

const readPoolHealth: ConnectionHealth = {
  isHealthy: true,
  lastCheck: new Date(),
  consecutiveFailures: 0,
};

// Helper function to create pool configuration with region-aware settings
function createPoolConfig(prefix: string, fallbackPrefix: string = 'PG'): PoolConfig {
  const getEnvVar = (suffix: string) => {
    return process.env[`${prefix}_${suffix}`] || process.env[`${fallbackPrefix}${suffix}`];
  };

  // Detect if this is a high-latency region (India, etc.)
  const instanceId = process.env.INSTANCE_ID || 'unknown';
  const region = process.env.REGION || 'unknown';
  const isHighLatencyRegion = region.toLowerCase().includes('india') ||
                              region.toLowerCase().includes('in') ||
                              instanceId.toLowerCase().includes('india') ||
                              instanceId.toLowerCase().includes('in') ||
                              // Also check if connecting to India server IP
                              getEnvVar('HOST')?.includes('*************');

  // Detect if this is a moderate-latency region (Hong Kong, Asia)
  const isModerateLatencyRegion = region.toLowerCase().includes('hk') ||
                                  region.toLowerCase().includes('hong') ||
                                  region.toLowerCase().includes('asia') ||
                                  instanceId.toLowerCase().includes('hk') ||
                                  instanceId.toLowerCase().includes('hong');

  // Region-specific timeout adjustments
  const timeoutMultiplier = isHighLatencyRegion ? 2.5 : (isModerateLatencyRegion ? 1.8 : 1.0);

  // Base timeouts
  const baseConnectionTimeout = parseInt(process.env.DB_CONNECTION_TIMEOUT || '10000');
  const baseAcquireTimeout = parseInt(process.env.DB_ACQUIRE_TIMEOUT || '5000');
  const baseStatementTimeout = parseInt(process.env.DB_STATEMENT_TIMEOUT || '45000');
  const baseQueryTimeout = parseInt(process.env.DB_QUERY_TIMEOUT || '45000');

  return {
    host: getEnvVar('HOST') || 'localhost',
    user: getEnvVar('USER') || 'postgres',
    password: getEnvVar('PASSWORD') || '',
    database: getEnvVar('DATABASE') || 'tempfly_app',
    port: parseInt(getEnvVar('PORT') || '5432'),
    ssl: (getEnvVar('SSLMODE') || process.env.PGSSLMODE) === 'disable' ? false : {
      rejectUnauthorized: false,
      requestCert: true
    },
    // Pool size adjustments for high-latency and moderate-latency regions
    max: parseInt(process.env.DB_POOL_MAX || (isHighLatencyRegion ? '12' : (isModerateLatencyRegion ? '10' : '20'))),
    min: parseInt(process.env.DB_POOL_MIN || (isHighLatencyRegion ? '2' : (isModerateLatencyRegion ? '2' : '3'))),
    idleTimeoutMillis: parseInt(process.env.DB_IDLE_TIMEOUT || (isHighLatencyRegion ? '120000' : (isModerateLatencyRegion ? '90000' : '60000'))),
    // Increased timeouts for high-latency regions
    connectionTimeoutMillis: Math.round(baseConnectionTimeout * timeoutMultiplier),
    acquireTimeoutMillis: Math.round(baseAcquireTimeout * timeoutMultiplier),
    keepAlive: true,
    statement_timeout: Math.round(baseStatementTimeout * timeoutMultiplier),
    query_timeout: Math.round(baseQueryTimeout * timeoutMultiplier),
    application_name: `tempfly-api-${instanceId}-${region}`,
  };
}

// Determine if read/write splitting is configured
const hasReadWriteConfig = !!(
  process.env.DB_WRITE_HOST && 
  process.env.DB_READ_HOST
);

// Create pool configurations
const writePoolConfig = hasReadWriteConfig 
  ? createPoolConfig('DB_WRITE', 'PG')
  : createPoolConfig('PG', 'PG');

const readPoolConfig = hasReadWriteConfig 
  ? createPoolConfig('DB_READ', 'PG')
  : createPoolConfig('PG', 'PG'); // Fallback to same as write if no read config

// Create connection pools
export const writePool = new Pool(writePoolConfig);
export const readPool = hasReadWriteConfig ? new Pool(readPoolConfig) : writePool;

// Apply safeguards to prevent accidental pool termination
preventPoolTermination(writePool, 'Write');
if (hasReadWriteConfig) {
  preventPoolTermination(readPool, 'Read');
}

// Log configuration with region information
const instanceId = process.env.INSTANCE_ID || 'unknown';
const region = process.env.REGION || 'unknown';
const isHighLatencyRegion = region.toLowerCase().includes('india') ||
                            region.toLowerCase().includes('in') ||
                            instanceId.toLowerCase().includes('india') ||
                            instanceId.toLowerCase().includes('in');

logger.info(`Database pools initialized for region: ${region}, instance: ${instanceId}`);
logger.info(`- Write pool: ${writePoolConfig.host}:${writePoolConfig.port}/${writePoolConfig.database}`);
logger.info(`- Connection timeout: ${writePoolConfig.connectionTimeoutMillis}ms`);
logger.info(`- Acquire timeout: ${writePoolConfig.acquireTimeoutMillis}ms`);
logger.info(`- Pool size: ${writePoolConfig.min}-${writePoolConfig.max} connections`);
logger.info(`- High latency region optimizations: ${isHighLatencyRegion ? 'ENABLED' : 'DISABLED'}`);

if (hasReadWriteConfig) {
  logger.info(`- Read pool: ${readPoolConfig.host}:${readPoolConfig.port}/${readPoolConfig.database}`);
  logger.info('Read/write splitting enabled');
} else {
  logger.info('- Read pool: Same as write pool (single database mode)');
  logger.info('Read/write splitting disabled - using single database');
}

// Check if a pool is in a usable state
function isPoolUsable(pool: Pool): boolean {
  try {
    // Check if pool has been ended
    if ((pool as any).ended) {
      return false;
    }

    // Check if pool has basic properties
    if (typeof pool.totalCount !== 'number' || typeof pool.idleCount !== 'number') {
      return false;
    }

    return true;
  } catch (error) {
    return false;
  }
}

// Enhanced health check function with latency monitoring and pool state validation
async function checkPoolHealth(pool: Pool, poolName: string): Promise<{ healthy: boolean; latency: number; error?: string }> {
  const startTime = Date.now();

  // First check if pool is in a usable state
  if (!isPoolUsable(pool)) {
    const errorMessage = 'Pool is in an unusable state (may have been ended)';
    logger.error(`${poolName} pool state check failed: ${errorMessage}`);
    return { healthy: false, latency: Date.now() - startTime, error: errorMessage };
  }

  try {
    const client = await pool.connect();
    const queryStart = Date.now();
    await client.query('SELECT 1 AS health_check');
    const queryLatency = Date.now() - queryStart;
    client.release();

    const totalLatency = Date.now() - startTime;

    // Log slow connections for monitoring
    if (totalLatency > 5000) {
      logger.warn(`Slow database connection detected for ${poolName}: ${totalLatency}ms (query: ${queryLatency}ms)`);
    }

    return { healthy: true, latency: totalLatency };
  } catch (error) {
    const totalLatency = Date.now() - startTime;
    const errorMessage = error instanceof Error ? error.message : String(error);

    // Check for specific "pool ended" error
    if (errorMessage.includes('pool after calling end') || errorMessage.includes('pool has been ended')) {
      logger.error(`${poolName} pool has been ended and is unusable: ${errorMessage}`);
    } else {
      logger.error(`Health check failed for ${poolName} after ${totalLatency}ms:`, error instanceof Error ? error : new Error(errorMessage));
    }

    return { healthy: false, latency: totalLatency, error: errorMessage };
  }
}

// Pool utilization monitoring
function logPoolUtilization(pool: Pool, poolName: string): void {
  const totalCount = pool.totalCount;
  const idleCount = pool.idleCount;
  const waitingCount = pool.waitingCount;
  const maxConnections = pool.options.max || 20;

  const utilizationPercent = Math.round((totalCount / maxConnections) * 100);

  // Log high utilization warnings
  if (utilizationPercent >= 80) {
    logger.warn(`High ${poolName} pool utilization: ${utilizationPercent}% (${totalCount}/${maxConnections} connections, ${waitingCount} waiting, ${idleCount} idle)`);
  } else if (utilizationPercent >= 60) {
    logger.info(`${poolName} pool utilization: ${utilizationPercent}% (${totalCount}/${maxConnections} connections, ${waitingCount} waiting, ${idleCount} idle)`);
  }
}

// Enhanced health status update with monitoring
async function updateHealthStatus(
  pool: Pool,
  poolName: string,
  health: ConnectionHealth
): Promise<void> {
  const healthResult = await checkPoolHealth(pool, poolName);

  // Log pool utilization during health checks
  logPoolUtilization(pool, poolName);

  if (healthResult.healthy) {
    if (!health.isHealthy) {
      logger.info(`${poolName} pool recovered (latency: ${healthResult.latency}ms)`);
    }
    health.isHealthy = true;
    health.consecutiveFailures = 0;

    // Log performance warnings for healthy but slow connections
    if (healthResult.latency > 10000) {
      logger.warn(`${poolName} pool is healthy but slow (${healthResult.latency}ms) - potential network issues`);
    }
  } else {
    health.isHealthy = false;
    health.consecutiveFailures++;

    logger.error(`${poolName} pool health check failed (attempt ${health.consecutiveFailures}/${FAILOVER_CONFIG.maxConsecutiveFailures}): ${healthResult.error}`);

    if (health.consecutiveFailures >= FAILOVER_CONFIG.maxConsecutiveFailures) {
      logger.error(`${poolName} pool marked as UNHEALTHY after ${health.consecutiveFailures} consecutive failures`);
      logger.error(`Last error: ${healthResult.error}`);

      // Log critical alert for monitoring systems
      logger.error(`CRITICAL: ${poolName} database pool completely failed - immediate attention required`);
    }
  }

  health.lastCheck = new Date();
}

// Circuit breaker logic
function updateCircuitBreaker(circuitBreaker: CircuitBreakerState, success: boolean): void {
  const now = new Date();

  if (success) {
    // Reset circuit breaker on success
    circuitBreaker.isOpen = false;
    circuitBreaker.failureCount = 0;
  } else {
    circuitBreaker.failureCount++;
    circuitBreaker.lastFailureTime = now;

    // Open circuit breaker after too many failures
    if (circuitBreaker.failureCount >= FAILOVER_CONFIG.maxConsecutiveFailures) {
      circuitBreaker.isOpen = true;
      // Exponential backoff for retry time
      const backoffMs = Math.min(30000, 1000 * Math.pow(2, circuitBreaker.failureCount - FAILOVER_CONFIG.maxConsecutiveFailures));
      circuitBreaker.nextRetryTime = new Date(now.getTime() + backoffMs);

      logger.warn(`Circuit breaker OPENED - will retry after ${backoffMs}ms`);
    }
  }
}

// Check if circuit breaker allows operation
function isCircuitBreakerOpen(circuitBreaker: CircuitBreakerState): boolean {
  if (!circuitBreaker.isOpen) return false;

  const now = new Date();
  if (now >= circuitBreaker.nextRetryTime) {
    // Time to try again - half-open state
    logger.info('Circuit breaker entering half-open state - attempting connection');
    return false;
  }

  return true;
}

// Safeguard function to prevent accidental pool termination
function preventPoolTermination(pool: Pool, poolName: string): void {
  // Override the end method to prevent accidental termination
  const originalEnd = pool.end.bind(pool);

  pool.end = async (...args: any[]) => {
    logger.error(`CRITICAL: Attempt to end ${poolName} pool detected - this would make the pool unusable!`);
    logger.error(`Stack trace: ${new Error().stack}`);
    logger.error(`Pool termination prevented - use proper shutdown procedures instead`);

    // Don't actually end the pool - just log the attempt
    return Promise.resolve();
  };

  // Store the original end method for proper shutdown
  (pool as any).__originalEnd = originalEnd;
}

// Pool recovery mechanism - attempts to recover without destroying the pool
async function attemptPoolRecovery(pool: Pool, poolName: string): Promise<boolean> {
  try {
    logger.info(`Attempting ${poolName} pool recovery...`);

    // First, try to test if the pool is still usable
    try {
      const testClient = await pool.connect();
      await testClient.query('SELECT 1 AS recovery_test');
      testClient.release();
      logger.info(`${poolName} pool recovery successful - pool is responsive`);
      return true;
    } catch (testError) {
      logger.warn(`${poolName} pool test failed during recovery: ${testError instanceof Error ? testError.message : String(testError)}`);
    }

    // If test failed, try to force cleanup of idle connections without ending the pool
    try {
      // Get current pool stats
      const totalBefore = pool.totalCount;
      const idleBefore = pool.idleCount;

      logger.info(`${poolName} pool stats before recovery: ${totalBefore} total, ${idleBefore} idle`);

      // Wait for any pending operations to complete
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Try another connection test after waiting
      const testClient = await pool.connect();
      await testClient.query('SELECT 1 AS recovery_test_2');
      testClient.release();

      logger.info(`${poolName} pool recovery completed successfully`);
      return true;
    } catch (recoveryError) {
      logger.error(`${poolName} pool recovery failed - pool may need manual intervention: ${recoveryError instanceof Error ? recoveryError.message : String(recoveryError)}`);
      return false;
    }
  } catch (error) {
    logger.error(`${poolName} pool recovery failed:`, error instanceof Error ? error : new Error(String(error)));
    return false;
  }
}

// Proper shutdown function for application termination
export async function shutdownDatabasePools(): Promise<void> {
  try {
    logger.info('Shutting down database pools...');

    // Use the original end methods to properly close pools
    if ((writePool as any).__originalEnd) {
      await (writePool as any).__originalEnd();
      logger.info('Write pool shut down successfully');
    }

    if (hasReadWriteConfig && (readPool as any).__originalEnd) {
      await (readPool as any).__originalEnd();
      logger.info('Read pool shut down successfully');
    }

    logger.info('All database pools shut down successfully');
  } catch (error) {
    logger.error('Error shutting down database pools:', error instanceof Error ? error : new Error(String(error)));
  }
}



// Connection warming function
async function warmupConnections(pool: Pool, poolName: string, count: number = 2): Promise<void> {
  const clients: PoolClient[] = [];
  try {
    logger.debug(`Warming up ${count} connections for ${poolName} pool`);

    // Acquire connections
    for (let i = 0; i < count; i++) {
      try {
        const client = await pool.connect();
        await client.query('SELECT 1'); // Simple query to establish connection
        clients.push(client);
      } catch (error) {
        const errorMsg = error instanceof Error ? error.message : String(error);
        logger.warn(`Failed to warm up connection ${i + 1} for ${poolName}: ${errorMsg}`);
        break;
      }
    }

    // Release all connections back to pool
    clients.forEach(client => client.release());
    logger.debug(`Successfully warmed up ${clients.length} connections for ${poolName} pool`);
  } catch (error) {
    // Release any acquired connections
    clients.forEach(client => {
      try {
        client.release();
      } catch (releaseError) {
        // Ignore release errors during cleanup
      }
    });
    logger.error(`Connection warmup failed for ${poolName}:`, error instanceof Error ? error : new Error(String(error)));
  }
}

// Enhanced periodic health checks with circuit breaker integration
setInterval(async () => {
  // Check write pool
  if (!isCircuitBreakerOpen(writePoolCircuitBreaker)) {
    const writeResult = await checkPoolHealth(writePool, 'Write');
    updateCircuitBreaker(writePoolCircuitBreaker, writeResult.healthy);

    if (writeResult.healthy) {
      writePoolHealth.isHealthy = true;
      writePoolHealth.consecutiveFailures = 0;
    } else {
      writePoolHealth.isHealthy = false;
      writePoolHealth.consecutiveFailures++;

      // Attempt recovery if too many failures
      if (writePoolHealth.consecutiveFailures >= FAILOVER_CONFIG.emergencyPoolRecreateThreshold) {
        logger.error(`Write pool has ${writePoolHealth.consecutiveFailures} consecutive failures - attempting emergency recovery`);

        // Only attempt recovery if pool is still usable
        if (isPoolUsable(writePool)) {
          await attemptPoolRecovery(writePool, 'Write');
        } else {
          logger.error('Write pool is in an unusable state - manual intervention required');
          logger.error('CRITICAL: Write pool needs to be restarted - consider restarting the application');
        }

        writePoolHealth.consecutiveFailures = 0; // Reset after recovery attempt
      }
    }

    logPoolUtilization(writePool, 'Write');
  } else {
    logger.debug('Write pool circuit breaker is open - skipping health check');
  }

  // Check read pool if configured
  if (hasReadWriteConfig && !isCircuitBreakerOpen(readPoolCircuitBreaker)) {
    const readResult = await checkPoolHealth(readPool, 'Read');
    updateCircuitBreaker(readPoolCircuitBreaker, readResult.healthy);

    if (readResult.healthy) {
      readPoolHealth.isHealthy = true;
      readPoolHealth.consecutiveFailures = 0;
    } else {
      readPoolHealth.isHealthy = false;
      readPoolHealth.consecutiveFailures++;

      // Attempt recovery if too many failures
      if (readPoolHealth.consecutiveFailures >= FAILOVER_CONFIG.emergencyPoolRecreateThreshold) {
        logger.error(`Read pool has ${readPoolHealth.consecutiveFailures} consecutive failures - attempting emergency recovery`);

        // Only attempt recovery if pool is still usable
        if (isPoolUsable(readPool)) {
          await attemptPoolRecovery(readPool, 'Read');
        } else {
          logger.error('Read pool is in an unusable state - manual intervention required');
          logger.error('CRITICAL: Read pool needs to be restarted - consider restarting the application');
        }

        readPoolHealth.consecutiveFailures = 0; // Reset after recovery attempt
      }
    }

    logPoolUtilization(readPool, 'Read');
  }
}, FAILOVER_CONFIG.healthCheckInterval);

// Connection warmup interval
setInterval(async () => {
  if (writePoolHealth.isHealthy && !isCircuitBreakerOpen(writePoolCircuitBreaker)) {
    await warmupConnections(writePool, 'Write', 2);
  }

  if (hasReadWriteConfig && readPoolHealth.isHealthy && !isCircuitBreakerOpen(readPoolCircuitBreaker)) {
    await warmupConnections(readPool, 'Read', 2);
  }
}, FAILOVER_CONFIG.connectionWarmupInterval);

// Pool event handlers for write pool
writePool.on('connect', () => {
  logger.debug('Write pool client connected');
});

writePool.on('error', (err: Error) => {
  logger.error('Write pool error:', err);
  writePoolHealth.isHealthy = false;
  writePoolHealth.consecutiveFailures++;

  // Log additional context for debugging
  logger.error(`Pool stats - Total: ${writePool.totalCount}, Idle: ${writePool.idleCount}, Waiting: ${writePool.waitingCount}`);
});

// Pool event handlers for read pool (if different from write pool)
if (hasReadWriteConfig) {
  readPool.on('connect', () => {
    logger.debug('Read pool client connected');
  });

  readPool.on('error', (err: Error) => {
    logger.error('Read pool error:', err);
    readPoolHealth.isHealthy = false;
    readPoolHealth.consecutiveFailures++;
  });
}

// Export health status getters
export const getWritePoolHealth = (): ConnectionHealth => ({ ...writePoolHealth });
export const getReadPoolHealth = (): ConnectionHealth => ({ ...readPoolHealth });

// Export configuration info
export const getDatabaseConfig = () => ({
  hasReadWriteSplitting: hasReadWriteConfig,
  writeConfig: {
    host: writePoolConfig.host,
    port: writePoolConfig.port,
    database: writePoolConfig.database,
  },
  readConfig: hasReadWriteConfig ? {
    host: readPoolConfig.host,
    port: readPoolConfig.port,
    database: readPoolConfig.database,
  } : null,
});
