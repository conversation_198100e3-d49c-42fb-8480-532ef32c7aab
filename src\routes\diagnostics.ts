import { Router, Request, Response } from 'express';
import { dbConnectionMonitor } from '../utils/databaseConnectionMonitor';
import { getWritePoolHealth, getReadPoolHealth, getDatabaseConfig } from '../config/database-pools';
import logger from '../utils/logger';
import { adminAuth } from '../middlewares/adminAuth';

const router = Router();

/**
 * Database connection diagnostics endpoint
 * Provides detailed information about database connection health
 */
router.get('/database', adminAuth, async (req: Request, res: Response) => {
  try {
    const requestId = (req as any).requestId || 'unknown';
    
    // Get comprehensive database health information
    const healthSummary = dbConnectionMonitor.getHealthSummary();
    const writePoolHealth = getWritePoolHealth();
    const readPoolHealth = getReadPoolHealth();
    const dbConfig = getDatabaseConfig();
    
    // Perform connection tests
    const writeConnectionTest = await dbConnectionMonitor.testConnection(10000); // 10s timeout for diagnostics
    
    // Get environment information
    const environmentInfo = {
      instanceId: process.env.INSTANCE_ID || 'unknown',
      region: process.env.REGION || 'unknown',
      nodeEnv: process.env.NODE_ENV || 'development',
      dbWriteHost: process.env.DB_WRITE_HOST || process.env.PGHOST || 'unknown',
      dbReadHost: process.env.DB_READ_HOST || process.env.PGHOST || 'unknown',
      connectionTimeout: process.env.DB_CONNECTION_TIMEOUT || '10000',
      acquireTimeout: process.env.DB_ACQUIRE_TIMEOUT || '5000',
      poolMax: process.env.DB_POOL_MAX || '20',
      poolMin: process.env.DB_POOL_MIN || '3',
    };
    
    const diagnostics = {
      'request-id': requestId,
      'message': 'Database diagnostics retrieved successfully',
      'data': {
        'summary': healthSummary,
        'pool-health': {
          'write-pool': writePoolHealth,
          'read-pool': readPoolHealth,
        },
        'configuration': {
          ...dbConfig,
          'environment': environmentInfo,
        },
        'connection-tests': {
          'write-connection': writeConnectionTest,
        },
        'recommendations': generateRecommendations(healthSummary, writeConnectionTest),
      }
    };
    
    res.status(200).json(diagnostics);
    
  } catch (error) {
    logger.error('Database diagnostics failed:', error as Error);
    res.status(500).json({
      'request-id': (req as any).requestId || 'unknown',
      'message': 'Failed to retrieve database diagnostics',
      'data': null,
      'error': error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Network connectivity test endpoint
 * Tests network connectivity to database servers
 */
router.get('/network', adminAuth, async (req: Request, res: Response) => {
  try {
    const requestId = (req as any).requestId || 'unknown';
    
    // Perform multiple connection tests with different timeouts
    const tests = await Promise.allSettled([
      dbConnectionMonitor.testConnection(1000),  // 1s - aggressive
      dbConnectionMonitor.testConnection(2000),  // 2s - normal
      dbConnectionMonitor.testConnection(5000),  // 5s - patient
      dbConnectionMonitor.testConnection(10000), // 10s - very patient
    ]);
    
    const networkDiagnostics = {
      'request-id': requestId,
      'message': 'Network diagnostics completed',
      'data': {
        'tests': tests.map((test, index) => ({
          'timeout': [1000, 2000, 5000, 10000][index],
          'result': test.status === 'fulfilled' ? test.value : { 
            success: false, 
            error: test.reason?.message || 'Test failed' 
          }
        })),
        'analysis': analyzeNetworkTests(tests),
        'timestamp': new Date().toISOString(),
      }
    };
    
    res.status(200).json(networkDiagnostics);
    
  } catch (error) {
    logger.error('Network diagnostics failed:', error as Error);
    res.status(500).json({
      'request-id': (req as any).requestId || 'unknown',
      'message': 'Network diagnostics failed',
      'data': null,
      'error': error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Real-time connection monitoring endpoint
 * Provides live connection statistics
 */
router.get('/monitor', adminAuth, async (req: Request, res: Response) => {
  try {
    const requestId = (req as any).requestId || 'unknown';
    const stats = dbConnectionMonitor.getConnectionStats();
    
    res.status(200).json({
      'request-id': requestId,
      'message': 'Connection monitoring data retrieved',
      'data': {
        'real-time-stats': stats,
        'timestamp': new Date().toISOString(),
      }
    });
    
  } catch (error) {
    logger.error('Connection monitoring failed:', error as Error);
    res.status(500).json({
      'request-id': (req as any).requestId || 'unknown',
      'message': 'Connection monitoring failed',
      'data': null,
      'error': error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Generate recommendations based on connection health
 */
function generateRecommendations(healthSummary: any, connectionTest: any): string[] {
  const recommendations: string[] = [];
  
  if (healthSummary.isHighLatencyRegion) {
    recommendations.push('High-latency region detected - using extended timeouts');
  }
  
  if (connectionTest.time > 2000) {
    recommendations.push('Connection time is high - consider optimizing network or using connection pooling');
  }
  
  if (healthSummary.connectionHealth.consecutiveFailures > 0) {
    recommendations.push('Recent connection failures detected - monitor network stability');
  }
  
  if (parseInt(healthSummary.connectionHealth.successRate) < 95) {
    recommendations.push('Connection success rate is below 95% - investigate network issues');
  }
  
  if (healthSummary.poolHealth.waitingClients > 0) {
    recommendations.push('Clients waiting for connections - consider increasing pool size');
  }
  
  if (recommendations.length === 0) {
    recommendations.push('Database connections appear healthy');
  }
  
  return recommendations;
}

/**
 * Analyze network test results
 */
function analyzeNetworkTests(tests: PromiseSettledResult<any>[]): any {
  const successful = tests.filter(t => t.status === 'fulfilled' && t.value.success).length;
  const failed = tests.length - successful;
  
  let analysis = {
    'success-rate': `${(successful / tests.length * 100).toFixed(1)}%`,
    'total-tests': tests.length,
    'successful': successful,
    'failed': failed,
    'recommendation': ''
  };
  
  if (successful === tests.length) {
    analysis.recommendation = 'All connection tests passed - network appears stable';
  } else if (successful >= tests.length / 2) {
    analysis.recommendation = 'Some connection tests failed - network may be unstable or timeouts too aggressive';
  } else {
    analysis.recommendation = 'Most connection tests failed - investigate network connectivity issues';
  }
  
  return analysis;
}

export default router;
