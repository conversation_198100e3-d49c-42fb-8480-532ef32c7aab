#!/usr/bin/env node

/**
 * Inbox Constraints Verification Script
 * 
 * This script checks the current database constraints on the inboxes table
 * to help diagnose the error handling issue.
 */

const { Pool } = require('pg');
require('dotenv').config();

// Database configuration
const pool = new Pool({
  host: process.env.PGHOST || 'localhost',
  port: parseInt(process.env.PGPORT || '5432'),
  database: process.env.PGDATABASE || 'tempfly_app',
  user: process.env.PGUSER || 'postgres',
  password: process.env.PGPASSWORD,
  ssl: process.env.PGSSLMODE === 'disable' ? false : { rejectUnauthorized: false },
});

async function checkConstraints() {
  let client;
  
  try {
    client = await pool.connect();
    console.log('🔍 Checking inbox table constraints...\n');
    
    // Check all constraints on inboxes table
    console.log('📋 All Constraints on inboxes table:');
    const constraintsQuery = `
      SELECT 
        conname as constraint_name,
        contype as constraint_type,
        CASE contype
          WHEN 'p' THEN 'PRIMARY KEY'
          WHEN 'u' THEN 'UNIQUE'
          WHEN 'f' THEN 'FOREIGN KEY'
          WHEN 'c' THEN 'CHECK'
          WHEN 'x' THEN 'EXCLUDE'
          ELSE contype::text
        END as constraint_type_desc,
        pg_get_constraintdef(oid) as constraint_definition
      FROM pg_constraint 
      WHERE conrelid = 'inboxes'::regclass
      ORDER BY contype, conname;
    `;
    
    const constraints = await client.query(constraintsQuery);
    
    if (constraints.rows.length === 0) {
      console.log('❌ No constraints found on inboxes table');
    } else {
      constraints.rows.forEach((row, index) => {
        console.log(`${index + 1}. ${row.constraint_name} (${row.constraint_type_desc})`);
        console.log(`   Definition: ${row.constraint_definition}`);
        console.log('');
      });
    }
    
    // Check all indexes on inboxes table
    console.log('📊 All Indexes on inboxes table:');
    const indexesQuery = `
      SELECT 
        indexname,
        indexdef,
        CASE 
          WHEN indisunique THEN 'UNIQUE'
          ELSE 'NON-UNIQUE'
        END as index_type
      FROM pg_indexes 
      JOIN pg_index ON pg_index.indexrelid = (schemaname||'.'||indexname)::regclass
      WHERE tablename = 'inboxes'
      ORDER BY indexname;
    `;
    
    const indexes = await client.query(indexesQuery);
    
    if (indexes.rows.length === 0) {
      console.log('❌ No indexes found on inboxes table');
    } else {
      indexes.rows.forEach((row, index) => {
        console.log(`${index + 1}. ${row.indexname} (${row.index_type})`);
        console.log(`   Definition: ${row.indexdef}`);
        console.log('');
      });
    }
    
    // Check for address-related constraints specifically
    console.log('🎯 Address-related constraints and indexes:');
    const addressConstraintsQuery = `
      SELECT 
        'constraint' as type,
        conname as name,
        pg_get_constraintdef(oid) as definition
      FROM pg_constraint 
      WHERE conrelid = 'inboxes'::regclass
      AND (conname LIKE '%address%' OR pg_get_constraintdef(oid) LIKE '%address%')
      
      UNION ALL
      
      SELECT 
        'index' as type,
        indexname as name,
        indexdef as definition
      FROM pg_indexes 
      WHERE tablename = 'inboxes'
      AND (indexname LIKE '%address%' OR indexdef LIKE '%address%')
      
      ORDER BY type, name;
    `;
    
    const addressConstraints = await client.query(addressConstraintsQuery);
    
    if (addressConstraints.rows.length === 0) {
      console.log('❌ No address-related constraints or indexes found');
    } else {
      addressConstraints.rows.forEach((row, index) => {
        console.log(`${index + 1}. ${row.name} (${row.type.toUpperCase()})`);
        console.log(`   Definition: ${row.definition}`);
        console.log('');
      });
    }
    
    // Test duplicate insertion to see what error we get
    console.log('🧪 Testing duplicate insertion behavior...');
    
    try {
      // First, clean up any existing test data
      await client.query("DELETE FROM inboxes WHERE address LIKE 'test-constraint-check%'");
      
      // Insert first record
      const testEmail = `test-constraint-check-${Date.now()}@tempfly.io`;
      await client.query(`
        INSERT INTO inboxes (name, domain, address, expiry_date, is_active)
        VALUES ('test-constraint-check', 'tempfly.io', $1, NOW() + INTERVAL '1 hour', true)
      `, [testEmail]);
      
      console.log(`✅ First insertion successful: ${testEmail}`);
      
      // Try to insert duplicate
      try {
        await client.query(`
          INSERT INTO inboxes (name, domain, address, expiry_date, is_active)
          VALUES ('test-constraint-check', 'tempfly.io', $1, NOW() + INTERVAL '1 hour', true)
        `, [testEmail]);
        
        console.log('❌ Duplicate insertion succeeded - this should not happen!');
        
      } catch (duplicateError) {
        console.log('✅ Duplicate insertion failed as expected:');
        console.log(`   Error Code: ${duplicateError.code}`);
        console.log(`   Constraint: ${duplicateError.constraint}`);
        console.log(`   Detail: ${duplicateError.detail}`);
        console.log(`   Message: ${duplicateError.message}`);
      }
      
      // Clean up test data
      await client.query("DELETE FROM inboxes WHERE address LIKE 'test-constraint-check%'");
      
    } catch (testError) {
      console.log('❌ Test insertion failed:');
      console.log(`   Error: ${testError.message}`);
    }
    
    // Summary and recommendations
    console.log('\n📝 Summary and Recommendations:');
    
    const uniqueConstraints = constraints.rows.filter(row => row.constraint_type === 'u');
    const uniqueIndexes = indexes.rows.filter(row => row.index_type === 'UNIQUE');
    
    console.log(`- Found ${uniqueConstraints.length} unique constraints`);
    console.log(`- Found ${uniqueIndexes.rows?.length || uniqueIndexes.length} unique indexes`);
    
    const hasOldConstraint = constraints.rows.some(row => row.constraint_name === 'inboxes_address_key');
    const hasNewIndex = indexes.rows.some(row => row.indexname === 'idx_inboxes_address_active_unique');
    
    if (hasOldConstraint && hasNewIndex) {
      console.log('⚠️  WARNING: Both old constraint and new index exist - this may cause issues');
      console.log('   Recommendation: Run the migration script to remove the old constraint');
    } else if (hasOldConstraint) {
      console.log('🔍 Found old constraint (inboxes_address_key) - this is likely causing the 500 error');
      console.log('   Recommendation: The error handling fix should resolve this');
    } else if (hasNewIndex) {
      console.log('✅ Found new partial unique index - this should work with current error handling');
    } else {
      console.log('❌ No address uniqueness enforcement found - this is a problem');
    }
    
  } catch (error) {
    console.error('❌ Error checking constraints:', error.message);
  } finally {
    if (client) {
      client.release();
    }
    await pool.end();
  }
}

// Run the check
checkConstraints().catch(error => {
  console.error('Script failed:', error);
  process.exit(1);
});
