import { getClient } from '../config/database';



import { PoolClient } from 'pg';



import { AppError } from '../middlewares/errorHandler';



import logger from '../utils/logger';



import { executeReadQuery, executeWriteQuery } from '../utils/databaseRouter';







export interface Inbox {



  id: string;



  name: string;



  domain: string;



  email: string;



  created_at: Date;



  expires_at: Date | null;



  is_active: boolean;



<<<<<<<
  api_key_id?: string;

=======
      // COMPREHENSIVE ERROR HANDLING: Handle all possible unique constraint violations

      if (error.code === '23505') {

        // Handle different constraint names that can cause duplicate key violations

        if (error.constraint === 'idx_inboxes_address_active_unique' ||

            error.constraint === 'inboxes_address_key' ||

            error.constraint === 'inboxes_address_unique') {

>>>>>>>


<<<<<<<
  rapidapi_key?: string;

=======
          // Extract the email from the error message with multiple patterns

          let emailAddress = `${name}@${domain}`;



          // Try different regex patterns to extract the email

          const patterns = [

            /\(address\)=\((.+?)\)/i,  // (address)=(<EMAIL>)

            /\(email\)=\((.+?)\)/i,   // (email)=(<EMAIL>)

            /Key \(address\)=\((.+?)\)/i, // Key (address)=(<EMAIL>)

            /Key \(email\)=\((.+?)\)/i    // Key (email)=(<EMAIL>)

          ];



          for (const pattern of patterns) {

            const match = error.detail?.match(pattern);

            if (match && match[1]) {

              emailAddress = match[1];

              break;

            }

          }



          // Throw a 400 Bad Request error with user-friendly message

          throw new AppError(`Inbox with address ${emailAddress} already exists. Please choose a different username.`, 400);

        }



        // Handle other potential unique constraints

        if (error.constraint && error.constraint.includes('address')) {

          // Generic handler for any address-related unique constraint

          const emailAddress = `${name}@${domain}`;

          throw new AppError(`Inbox with address ${emailAddress} already exists. Please choose a different username.`, 400);

        }

      }

>>>>>>>


  // PHASE 3: Removed forwarding fields since functionality is disabled



}







export class InboxModel {



  /**



   * Create a new inbox



   */



  static async create(



    name: string,



    domain: string,



    expiresAt: Date | null = null,



    apiKeyId: string | null = null,



    rapidApiKey: string | null = null



  ): Promise<Inbox> {



    let client: PoolClient | undefined;







    try {



      const email = `${name}@${domain}`;







      // Log the inbox creation details for debugging



      const logger = require('../utils/logger').default;



      logger.info(`Creating inbox with name: "${name}", domain: "${domain}", expiry date: ${expiresAt ? expiresAt.toISOString() : 'null'}`);







      client = await getClient('write');



      await client.query('BEGIN');







      // First, try to reactivate any existing inactive inbox with the same address



      const reactivateQuery = `



        UPDATE inboxes



        SET is_active = true,



            expiry_date = $1,



            api_key_id = $2,



            rapidapi_key = $3,



            updated_at = CURRENT_TIMESTAMP



        WHERE address = $4



        AND is_active = false



        RETURNING *



      `;







      const reactivateResult = await client.query(reactivateQuery, [expiresAt, apiKeyId, rapidApiKey, email]);







      if (reactivateResult.rowCount && reactivateResult.rowCount > 0) {



        // Successfully reactivated an existing inbox



        logger.info(`Reactivated existing inbox: ${email}`);



        await client.query('COMMIT');



        return this.mapDbToModel(reactivateResult.rows[0]);



      }







      // No existing inactive inbox found, create a new one



      const createQuery = `



        INSERT INTO inboxes (name, domain, address, expiry_date, api_key_id, rapidapi_key)



        VALUES ($1, $2, $3, $4, $5, $6)



        RETURNING *



      `;







      const createResult = await client.query(createQuery, [name, domain, email, expiresAt, apiKeyId, rapidApiKey]);







      // Log the result for debugging



      logger.info(`New inbox created in database with expiry_date: ${createResult.rows[0].expiry_date}`);







      await client.query('COMMIT');







      // Map database column names to model property names



      return this.mapDbToModel(createResult.rows[0]);



    } catch (error: any) {



      if (client) {



        await client.query('ROLLBACK');



      }







      // Add more context to the error



      if (error.code === '23505') {



        if (error.constraint === 'idx_inboxes_address_active_unique') {



          // Extract the email from the error message if possible



          const emailMatch = error.detail?.match(/\((address)\)=\((.+?)\)/i);



          const emailAddress = emailMatch ? emailMatch[2] : `${name}@${domain}`;







          // Throw a 400 Bad Request error instead of a 500 Internal Server Error



          throw new AppError(`Active inbox with email ${emailAddress} already exists`, 400);



        }



      }







      // If it's already an AppError, just rethrow it



      if (error instanceof AppError) {



        throw error;



      }







      // Log more details about the error



      const logger = require('../utils/logger').default;



      logger.error(`Error creating inbox with name: "${name}", domain: "${domain}"`, {



        error: error.message,



        code: error.code,



        detail: error.detail,



        constraint: error.constraint



      });







      throw error;



    } finally {



      if (client) {



        // TypeScript doesn't recognize that release returns a Promise in our custom implementation



        // @ts-ignore



        await client.release();



      }



    }



  }







  /**



   * Get inbox by ID with timeout protection



   */



  static async getById(id: string): Promise<Inbox | null> {



    const startTime = Date.now();







    try {



      const query = `



        SELECT * FROM inboxes



        WHERE id = $1



        AND is_active = true



        AND (expiry_date IS NULL OR expiry_date > NOW())



      `;







      // Use read pool for SELECT query with timeout protection



      const queryPromise = executeReadQuery(query, [id]);



      const timeoutPromise = new Promise<never>((_, reject) => {



        setTimeout(() => {



          reject(new Error(`Inbox getById query timeout after 3 seconds for ID ${id}`));



        }, 3000); // 3 second timeout for inbox lookup



      });







      // Race the query against the timeout



      const result = await Promise.race([queryPromise, timeoutPromise]);







      const executionTime = Date.now() - startTime;







      // Performance monitoring



      if (executionTime > 1000) {



        logger.warn(`Slow inbox getById query detected: ${executionTime}ms for ID ${id}`);



      }







      if (result.rows.length === 0) {



        return null;



      }







      // Map database column names to model property names



      const dbInbox = result.rows[0];



      return this.mapDbToModel(dbInbox);



    } catch (error) {



      const executionTime = Date.now() - startTime;



      logger.error(`Error getting inbox by ID ${id} (${executionTime}ms):`, error instanceof Error ? error : new Error(String(error)));



      throw error;



    }



  }







  /**



   * Get inbox by email



   */



  static async getByEmail(email: string): Promise<Inbox | null> {



    const query = `



      SELECT * FROM inboxes



      WHERE address = $1



      AND is_active = true



      AND (expiry_date IS NULL OR expiry_date > NOW())



    `;



    const result = await executeReadQuery(query, [email]);







    if (result.rows.length === 0) {



      return null;



    }







    // Map database column names to model property names



    const dbInbox = result.rows[0];



    return this.mapDbToModel(dbInbox);



  }







  /**



   * List all inboxes with pagination



   */



  static async list(



    page: number = 1,



    pageSize: number = 10,



    nameFilter?: string,



    apiKeyId?: string,



    rapidApiKey?: string



  ): Promise<{ inboxes: Inbox[]; total: number }> {



    // Optimize query to select only needed columns instead of SELECT *



    let query = `



      SELECT



        id, name, domain, address, created_at, expiry_date,



        is_active, api_key_id, rapidapi_key



      FROM inboxes



      WHERE is_active = true



      AND (expiry_date IS NULL OR expiry_date > NOW())



    `;







    // Use a more efficient count query with an index-only scan



    let countQuery = 'SELECT COUNT(id) FROM inboxes WHERE is_active = true AND (expiry_date IS NULL OR expiry_date > NOW())';







    const values: any[] = [];



    const countValues: any[] = [];



    const conditions: string[] = [];



    const countConditions: string[] = [];







    // Add name filter if provided and not empty



    if (nameFilter && nameFilter.trim() !== '') {



      const cleanFilter = nameFilter.trim();



      conditions.push(`name ILIKE $${values.length + 1}`);



      values.push(`%${cleanFilter}%`);







      // Also add to count query



      countConditions.push(`name ILIKE $${countValues.length + 1}`);



      countValues.push(`%${cleanFilter}%`);



    }







    // Add API key filter if provided



    if (apiKeyId) {



      conditions.push(`api_key_id = $${values.length + 1}`);



      values.push(apiKeyId);







      // Also add to count query



      countConditions.push(`api_key_id = $${countValues.length + 1}`);



      countValues.push(apiKeyId);



    }







    // Add RapidAPI key filter if provided



    if (rapidApiKey) {



      conditions.push(`rapidapi_key = $${values.length + 1}`);



      values.push(rapidApiKey);







      // Also add to count query



      countConditions.push(`rapidapi_key = $${countValues.length + 1}`);



      countValues.push(rapidApiKey);



    }







    // Add conditions to queries



    if (conditions.length > 0) {



      query += ` AND ${conditions.join(' AND ')}`;



    }







    if (countConditions.length > 0) {



      countQuery += ` AND ${countConditions.join(' AND ')}`;



    }







    // Add pagination with optimized ordering



    // Use index on created_at for faster sorting



    query += ` ORDER BY created_at DESC LIMIT $${values.length + 1} OFFSET $${values.length + 2}`;



    values.push(pageSize);



    values.push((page - 1) * pageSize);







    try {



      // Execute both queries in parallel using read pool for better performance



      const [inboxesResult, countResult] = await Promise.all([



        executeReadQuery(query, values),



        executeReadQuery(countQuery, countValues),



      ]);







      // Process results efficiently



      const inboxes = inboxesResult.rows.map(dbInbox => this.mapDbToModel(dbInbox));



      const total = parseInt(countResult.rows[0].count, 10);







      return { inboxes, total };



    } catch (error) {



      logger.error('Error listing inboxes:', error instanceof Error ? error : new Error(String(error)));



      throw error;



    }



  }







  /**



   * Delete an inbox



   *



   * This will cascade delete all associated emails and attachments



   * due to the foreign key constraints with ON DELETE CASCADE



   */



  static async delete(id: string): Promise<boolean> {



    const query = 'DELETE FROM inboxes WHERE id = $1 RETURNING id';



    const result = await executeWriteQuery(query, [id]);







    return result.rowCount !== null && result.rowCount > 0;



  }







  /**



   * Deactivate an inbox by ID and RapidAPI key (for limit enforcement)



   * This method is used to enforce inbox limits by deactivating excess inboxes



   */



  static async deactivate(id: string, rapidApiKey: string): Promise<boolean> {



    const startTime = Date.now();



    let client;







    try {



      client = await getClient('write');



      await client.query('BEGIN');







      // First, verify the inbox exists and belongs to the user



      const verifyQuery = `



        SELECT id, name, address, is_active



        FROM inboxes



        WHERE id = $1 AND rapidapi_key = $2



      `;







      const verifyResult = await client.query(verifyQuery, [id, rapidApiKey]);







      if (verifyResult.rows.length === 0) {



        await client.query('ROLLBACK');



        logger.warn(`Deactivation failed: Inbox ${id} not found or doesn't belong to ${rapidApiKey}`);



        return false;



      }







      const inbox = verifyResult.rows[0];







      if (!inbox.is_active) {



        await client.query('ROLLBACK');



        logger.debug(`Inbox ${id} is already inactive`);



        return true; // Already inactive, consider this success



      }







      // Deactivate the inbox



      const deactivateQuery = `



        UPDATE inboxes



        SET is_active = false,



            updated_at = NOW()



        WHERE id = $1 AND rapidapi_key = $2



        RETURNING id, name, address



      `;







      const deactivateResult = await client.query(deactivateQuery, [id, rapidApiKey]);







      if (deactivateResult.rows.length === 0) {



        await client.query('ROLLBACK');



        logger.error(`Failed to deactivate inbox ${id} for ${rapidApiKey}`);



        return false;



      }







      await client.query('COMMIT');







      const executionTime = Date.now() - startTime;



      const deactivatedInbox = deactivateResult.rows[0];







      // Log successful deactivation



      logger.info(`Successfully deactivated inbox ${id} (${deactivatedInbox.address}) for ${rapidApiKey} in ${executionTime}ms`);







      // Performance monitoring



      if (executionTime > 2000) {



        logger.warn(`Slow inbox deactivation: ${executionTime}ms for inbox ${id}`);



      }







      return true;







    } catch (error) {



      if (client) {



        try {



          await client.query('ROLLBACK');



        } catch (rollbackError) {



          logger.error('Error rolling back deactivation transaction:', rollbackError instanceof Error ? rollbackError : new Error(String(rollbackError)));



        }



      }







      const executionTime = Date.now() - startTime;



      logger.error(`Error deactivating inbox ${id} for ${rapidApiKey} (${executionTime}ms):`, error instanceof Error ? error : new Error(String(error)));







      // Re-throw the error so calling code can handle it appropriately



      throw error;







    } finally {



      if (client) {



        client.release();



      }



    }



  }







  /**



   * Clean up expired inboxes



   *



   * Note: This is now a fallback mechanism as the primary cleanup



   * is handled by pg_cron scheduled jobs in the database.



   *



   * This method now only marks expired inboxes as inactive rather than



   * deleting them, which is safer and allows for recovery if needed.



   */



  static async cleanupExpired(): Promise<number> {



    const startTime = Date.now();







    // This query now matches what the pg_cron job does



    const query = `



      UPDATE inboxes



      SET is_active = false



      WHERE expiry_date IS NOT NULL



      AND expiry_date < NOW()



      AND is_active = true



      RETURNING id



    `;







    const result = await executeWriteQuery(query);



    const executionTime = Date.now() - startTime;







    // Log the cleanup operation to the maintenance_logs table



    if (result.rowCount && result.rowCount > 0) {



      try {



        await executeWriteQuery(



          `INSERT INTO maintenance_logs (



            operation,



            details,



            affected_rows,



            execution_time,



            status



          ) VALUES ($1, $2, $3, $4, $5)`,



          [



            'cleanup_expired_inboxes_api',



            'API-triggered cleanup of expired inboxes',



            result.rowCount,



            executionTime,



            'success'



          ]



        );







        logger.info(`Cleaned up ${result.rowCount} expired inboxes in ${executionTime}ms`);



      } catch (error) {



        // If the maintenance_logs table doesn't exist yet, just ignore the error



        const errorMsg = error instanceof Error ? error.message : String(error);



        logger.debug(`Could not log to maintenance_logs table: ${errorMsg}`);



      }



    } else {



      logger.debug('No expired inboxes to clean up');



    }







    return result.rowCount || 0;



  }







  /**



   * Get information about scheduled cleanup jobs



   * This is useful for monitoring and debugging



   */



  static async getCleanupJobStatus(): Promise<any> {



    try {



      // Query the cron.job table to get information about scheduled jobs



      const cronJobsQuery = `



        SELECT



          jobid,



          jobname,



          schedule,



          command,



          nodename,



          nodeport,



          database,



          username,



          active



        FROM cron.job



        WHERE jobname LIKE 'cleanup%'



      `;







      const cronJobsResult = await executeReadQuery(cronJobsQuery);







      // Query the maintenance_logs table to get information about recent cleanup runs



      const logsQuery = `



        SELECT



          operation,



          executed_at,



          affected_rows,



          execution_time,



          status,



          error_message



        FROM maintenance_logs



        WHERE operation LIKE 'cleanup%'



        ORDER BY executed_at DESC



        LIMIT 10



      `;







      const logsResult = await executeReadQuery(logsQuery);







      return {



        scheduledJobs: cronJobsResult.rows,



        recentRuns: logsResult.rows



      };



    } catch (error) {



      const errorMessage = 'Error getting cleanup job status:';



      const errorObj = error instanceof Error ? error : new Error(String(error));



      logger.error(errorMessage, errorObj);



      throw error;



    }



  }







  /**



   * Check if an inbox belongs to an API key



   */



  static async belongsToApiKey(inboxId: string, apiKeyId: string): Promise<boolean> {



    const query = 'SELECT id FROM inboxes WHERE id = $1 AND api_key_id = $2 AND is_active = true';



    const result = await executeReadQuery(query, [inboxId, apiKeyId]);



    return result.rows.length > 0;



  }







  /**



   * Check if an inbox belongs to a RapidAPI key



   */



  static async belongsToRapidApiKey(inboxId: string, rapidApiKey: string): Promise<boolean> {



    const query = 'SELECT id FROM inboxes WHERE id = $1 AND rapidapi_key = $2 AND is_active = true';



    const result = await executeReadQuery(query, [inboxId, rapidApiKey]);



    return result.rows.length > 0;



  }







  /**



   * Count active inboxes for a RapidAPI key



   * PERFORMANCE OPTIMIZED: Uses optimized query structure for faster execution



   */



  static async countActiveInboxesByRapidApiKey(rapidApiKey: string): Promise<number> {



    // PERFORMANCE OPTIMIZATION: Optimized query with better index usage



    const query = `



      SELECT COUNT(*) as count FROM inboxes



      WHERE rapidapi_key = $1



      AND is_active = true



      AND (expiry_date IS NULL OR expiry_date > CURRENT_TIMESTAMP)



    `;







    try {



      const result = await executeReadQuery(query, [rapidApiKey]);



      return parseInt(result.rows[0].count, 10);



    } catch (error) {



      logger.error('Error counting active inboxes:', error instanceof Error ? error : new Error(String(error)));



      throw error;



    }



  }







  /**



   * Count active inboxes for a RapidAPI key with inline cleanup of expired inboxes



   * This method ensures the most accurate count by cleaning up expired inboxes first



   */



  static async countActiveInboxesByRapidApiKeyWithCleanup(rapidApiKey: string): Promise<number> {



    const startTime = Date.now();



    let client;







    try {



      client = await getClient('write');



      await client.query('BEGIN');







      // First, clean up any expired inboxes for this user to get accurate count



      const cleanupQuery = `



        UPDATE inboxes



        SET is_active = false



        WHERE rapidapi_key = $1



        AND is_active = true



        AND expiry_date IS NOT NULL



        AND expiry_date <= NOW()



        RETURNING id



      `;







      const cleanupResult = await client.query(cleanupQuery, [rapidApiKey]);



      const cleanedCount = cleanupResult.rowCount || 0;







      // Now get the accurate count of active inboxes



      const countQuery = `



        SELECT COUNT(*) FROM inboxes



        WHERE rapidapi_key = $1



        AND is_active = true



        AND (expiry_date IS NULL OR expiry_date > NOW())



      `;







      const countResult = await client.query(countQuery, [rapidApiKey]);



      const activeCount = parseInt(countResult.rows[0].count, 10);







      await client.query('COMMIT');







      const executionTime = Date.now() - startTime;







      // Log cleanup and performance info



      if (cleanedCount > 0) {



        logger.info(`Cleaned up ${cleanedCount} expired inboxes for ${rapidApiKey} during limit check`);



      }







      logger.debug(`Inbox count with cleanup completed in ${executionTime}ms: ${activeCount} active inboxes for ${rapidApiKey}`);







      // Performance monitoring



      if (executionTime > 3000) {



        logger.warn(`Slow inbox count with cleanup: ${executionTime}ms for ${rapidApiKey}`);



      }







      return activeCount;







    } catch (error) {



      if (client) {



        try {



          await client.query('ROLLBACK');



        } catch (rollbackError) {



          logger.error('Error rolling back transaction:', rollbackError instanceof Error ? rollbackError : new Error(String(rollbackError)));



        }



      }







      const executionTime = Date.now() - startTime;



      logger.error(`Error in countActiveInboxesByRapidApiKeyWithCleanup (${executionTime}ms):`, error instanceof Error ? error : new Error(String(error)));







      // Fallback to regular count method



      logger.info('Falling back to regular count method');



      return this.countActiveInboxesByRapidApiKey(rapidApiKey);







    } finally {



      if (client) {



        client.release();



      }



    }



  }















  /**



   * Map database column names to model property names



   * PHASE 3: Simplified mapping without forwarding fields



   */



  static mapDbToModel(dbInbox: any): Inbox {



    // Direct mapping without intermediate object creation for better performance



    return {



      id: dbInbox.id,



      name: dbInbox.name,



      domain: dbInbox.domain,



      email: dbInbox.address || dbInbox.email,



      created_at: dbInbox.created_at,



      expires_at: dbInbox.expiry_date, // Map expiry_date to expires_at



      is_active: dbInbox.is_active,



      api_key_id: dbInbox.api_key_id,



      rapidapi_key: dbInbox.rapidapi_key



    };



  }



}







